import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import "@testing-library/jest-dom/vitest";
import { TEST_CONFIG } from "../../../__mocks__/badge.mock";
import { Badge, type BadgeConfig } from "../../../src/components/common";

type TestType =
  | "success"
  | "warning"
  | "error"
  | "icon-success"
  | "icon-warning";

describe("Generic Badge Component", () => {
  it("should render nothing when no type is provided", () => {
    const { container } = render(<Badge config={TEST_CONFIG} />);
    expect(container.firstChild).toBeNull();
  });

  it("should render success badge correctly", () => {
    render(<Badge type="success" config={TEST_CONFIG} />);

    expect(screen.getByText("SUCCESS")).toBeInTheDocument();
    expect(screen.getByAltText("Success Icon")).toBeInTheDocument();
    expect(screen.getByText("SUCCESS")).toHaveClass("text-green-700");
  });

  it("should render warning badge correctly", () => {
    render(<Badge type="warning" config={TEST_CONFIG} />);

    expect(screen.getByText("WARNING")).toBeInTheDocument();
    expect(screen.getByAltText("Warning Icon")).toBeInTheDocument();
    expect(screen.getByText("WARNING")).toHaveClass("text-yellow-700");
  });

  it("should render error badge correctly", () => {
    render(<Badge type="error" config={TEST_CONFIG} />);

    expect(screen.getByText("ERROR")).toBeInTheDocument();
    expect(screen.getByAltText("Error Icon")).toBeInTheDocument();
    expect(screen.getByText("ERROR")).toHaveClass("text-red-700");
  });

  it("should apply custom className", () => {
    const { container } = render(
      <Badge type="success" config={TEST_CONFIG} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should apply base classes correctly", () => {
    const { container } = render(<Badge type="success" config={TEST_CONFIG} />);
    const badge = container.firstChild as HTMLElement;

    expect(badge).toHaveClass("badge", "badge-soft", "badge-primary");
  });

  it("should apply configuration classes correctly", () => {
    const { container } = render(<Badge type="success" config={TEST_CONFIG} />);
    const badge = container.firstChild as HTMLElement;

    expect(badge).toHaveClass("border-green-500", "bg-green-100");
  });

  it("should render icon with correct src and alt", () => {
    render(<Badge type="warning" config={TEST_CONFIG} />);

    const icon = screen.getByAltText("Warning Icon") as HTMLImageElement;
    expect(icon.src).toContain("warning-icon.png");
    expect(icon).toHaveClass("size-4");
  });

  it("should render text with correct classes", () => {
    render(<Badge type="error" config={TEST_CONFIG} />);

    const text = screen.getByText("ERROR");
    expect(text).toHaveClass("font-semibold", "text-sm", "text-red-700");
  });

  it("should work with different badge types", () => {
    const types: TestType[] = ["success", "warning", "error"];

    types.forEach((type) => {
      const { container, unmount } = render(
        <Badge type={type} config={TEST_CONFIG} />
      );

      expect(container.firstChild).toBeInTheDocument();
      expect(screen.getByText(TEST_CONFIG[type].label!)).toBeInTheDocument();
      expect(
        screen.getByAltText(TEST_CONFIG[type].iconAlt!)
      ).toBeInTheDocument();

      unmount();
    });
  });

  it("should render ReactNode icons correctly", () => {
    render(<Badge type="icon-success" config={TEST_CONFIG} />);

    expect(screen.getByText("SUCCESS")).toBeInTheDocument();
    const iconContainer = document.querySelector(".ri-check-line");
    expect(iconContainer).toBeInTheDocument();
  });

  it("should render ReactNode icons with correct wrapper", () => {
    const { container } = render(
      <Badge type="icon-warning" config={TEST_CONFIG} />
    );

    const iconWrapper = container.querySelector("div > .ri-alert-line");
    expect(iconWrapper).toBeInTheDocument();
  });

  it("should handle missing icon gracefully", () => {
    const configWithoutIcon: Record<"no-icon", BadgeConfig> = {
      "no-icon": {
        containerClasses: "border-gray-500 bg-gray-100",
        label: "NO ICON",
        textClasses: "text-gray-700",
      },
    };

    render(<Badge type="no-icon" config={configWithoutIcon} />);

    expect(screen.getByText("NO ICON")).toBeInTheDocument();
    // Should not render any icon
    expect(document.querySelector("img")).not.toBeInTheDocument();
    expect(document.querySelector(".size-4")).not.toBeInTheDocument();
  });

  it("should handle missing label gracefully", () => {
    const configWithoutLabel: Record<"no-label", BadgeConfig> = {
      "no-label": {
        containerClasses: "border-gray-500 bg-gray-100",
        icon: "test-icon.png",
        iconAlt: "Test Icon",
        textClasses: "text-gray-700",
      },
    };

    render(<Badge type="no-label" config={configWithoutLabel} />);

    expect(screen.getByAltText("Test Icon")).toBeInTheDocument();
    expect(document.querySelector("span")).not.toBeInTheDocument();
  });

  it("should handle missing iconAlt gracefully", () => {
    const configWithoutIconAlt: Record<"no-icon-alt", BadgeConfig> = {
      "no-icon-alt": {
        containerClasses: "border-gray-500 bg-gray-100",
        icon: "test-icon.png",
        label: "TEST",
        textClasses: "text-gray-700",
      },
    };

    render(<Badge type="no-icon-alt" config={configWithoutIconAlt} />);

    expect(screen.getByText("TEST")).toBeInTheDocument();
    const icon = document.querySelector("img") as HTMLImageElement;
    expect(icon).toBeInTheDocument();
    expect(icon.alt).toBe("");
  });
});
