import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import "@testing-library/jest-dom/vitest";
import {
  OpportunityBadge,
  type OpportunityType,
} from "../../../src/components/badge/opportunityBadge";

describe("OpportunityBadge", () => {
  it("should render nothing when no type is provided", () => {
    const { container } = render(<OpportunityBadge />);
    expect(container.firstChild).toBeNull();
  });

  it("should render hot badge correctly", () => {
    render(<OpportunityBadge type="hot" />);

    expect(screen.getByText("HOT")).toBeInTheDocument();
    expect(screen.getByAltText("Sun")).toBeInTheDocument();
    expect(screen.getByText("HOT")).toHaveClass("text-error-content");
  });

  it("should render warm badge correctly", () => {
    render(<OpportunityBadge type="warm" />);

    expect(screen.getByText("WARM")).toBeInTheDocument();
    expect(screen.getByAltText("Moon")).toBeInTheDocument();
    expect(screen.getByText("WARM")).toHaveClass("text-warning-content");
  });

  it("should render cold badge correctly", () => {
    render(<OpportunityBadge type="cold" />);

    expect(screen.getByText("COLD")).toBeInTheDocument();
    expect(screen.getByAltText("Cloudy")).toBeInTheDocument();
    expect(screen.getByText("COLD")).toHaveClass("text-primary");
  });

  it("should apply custom className", () => {
    const { container } = render(
      <OpportunityBadge type="hot" className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should apply custom className with type", () => {
    const { container } = render(
      <OpportunityBadge type="hot" className="custom-container" />
    );

    expect(container.firstChild).toHaveClass("custom-container");
  });

  it("should have correct base classes for all badge types", () => {
    const types: OpportunityType[] = ["hot", "warm", "cold"];

    types.forEach((type) => {
      const { container, unmount } = render(<OpportunityBadge type={type} />);
      const badge = container.firstChild as HTMLElement;

      expect(badge).toHaveClass("badge", "badge-soft", "badge-primary");

      unmount();
    });
  });
});
