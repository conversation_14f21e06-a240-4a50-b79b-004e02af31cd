{"editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.organizeImports.biome": "explicit", "source.action.useSortedKeys.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "biomejs.biome", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "debug.javascript.terminalOptions": {"resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**", "**/node_modules/.vite-temp/**"]}, "javascript.suggest.autoImports": true, "typescript.suggest.autoImports": true, "typescript.experimental.useTsgo": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.tsserver.experimental.enableProjectDiagnostics": true, "tasks.terminateOnExit": true, "biome.enable": true, "biome.enableDiagnostics": true, "biome.enableFormatting": true, "biome.enableCodeActions": true, "biome.enableOrganizeImports": true, "biome.enableLinting": true, "biome.enableTypeChecking": true, "biome.enableAutoFixes": true}