import { Badge, type BadgeConfig } from "@components/common";
import { cn } from "@utils/cn";

export type UserStatusType = "active" | "completed" | "draft" | "suspended";

interface UserStatusBadgeConfig extends BadgeConfig {
  type: UserStatusType;
}

const USER_STATUS_BADGE_CONFIG: Record<UserStatusType, UserStatusBadgeConfig> = {
  active: {
    containerClasses: "bg-rose-50 !text-label-xs",
    icon: <div className="size-2 rounded-full bg-error" />,
    iconAlt: "Active",
    label: "ACTIVE",
    textClasses: "text-secondary",
    type: "active",
  },
  completed: {
    containerClasses: "bg-success-content !text-label-xs",
    icon: <div className="size-2 rounded-full bg-primary" />,
    iconAlt: "Completed",
    label: "COMPLETED",
    textClasses: "text-primary",
    type: "completed",
  },
  draft: {
    containerClasses: "bg-amber-50",
    icon: <div className="size-2 rounded-full bg-warning-content" />,
    iconAlt: "Draft",
    label: "DRAFT",
    textClasses: "text-warning-content",
    type: "draft",
  },
  suspended: {
    containerClasses: "bg-base-200",
    icon: <div className="size-2 rounded-full bg-neutral" />,
    iconAlt: "Suspended",
    label: "SUSPENDED",
    textClasses: "text-neutral",
    type: "suspended",
  },
};

interface UserStatusBadgeProps {
  type?: UserStatusType;
  className?: string;
}

export const UserStatusBadge = ({ type, className }: UserStatusBadgeProps) => {
  return (
    <Badge
      type={type}
      className={cn("border-neutral", className)}
      config={USER_STATUS_BADGE_CONFIG}
    />
  );
};

export const USER_STATUS_OPTIONS = (
  ["active", "completed", "draft", "suspended"] as UserStatusType[]
).map((type) => ({
  label: <UserStatusBadge type={type} />,
  value: type,
}));
