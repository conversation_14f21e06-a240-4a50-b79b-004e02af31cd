import {
  CONTACT_CHANNEL_OPTIONS,
  FOLLOW_UP_STATUS_OPTIONS,
  OPPORTUNITY_OPTIONS,
  SERVICE_OPTIONS,
} from "@components/badge";
import type { FormField } from "./interface";

export const addLeadFormField = (t: (key: string) => string) => {
  const today = new Date().toISOString().split("T")[0];

  const formFields: FormField[] = [
    {
      id: "name",
      label: t("addLead.name"),
      placeholder: t("addLead.name"),
      required: true,
      type: "input",
      variant: "transparent",
    },
    {
      id: "contactChannel",
      label: t("addLead.contactChannel"),
      options: CONTACT_CHANNEL_OPTIONS,
      placeholder: t("addLead.contactChannel"),
      required: true,
      type: "select",
    },
    {
      id: "servicesOfInterest",
      label: t("addLead.servicesOfInterest"),
      options: SERVICE_OPTIONS,
      placeholder: t("addLead.servicesOfInterest"),
      type: "select",
    },
    {
      id: "opportunity",
      label: t("addLead.opportunity"),
      options: OPPORTUNITY_OPTIONS,
      placeholder: t("addLead.opportunity"),
      type: "select",
    },
    {
      id: "followUpStatus",
      label: t("addLead.followUpStatus"),
      options: FOLLOW_UP_STATUS_OPTIONS,
      placeholder: t("addLead.followUpStatus"),
      type: "select",
    },
    {
      disabled: true,
      id: "followUpDate",
      label: t("addLead.followUpDate"),
      type: "input",
    },
    {
      id: "contactInfo",
      label: t("addLead.contactInfo"),
      placeholder: t("addLead.contactInfo"),
      type: "input",
      variant: "transparent",
    },
    {
      defaultValue: today,
      id: "startDate",
      label: t("addLead.startDate"),
      type: "date",
    },
  ];

  return formFields;
};
