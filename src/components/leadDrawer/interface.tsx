export type FieldType = "input" | "select" | "date" | "textarea";

export interface BaseField {
  id: string;
  label: string;
  type: FieldType;
  required?: boolean;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export interface InputField extends BaseField {
  type: "input" | "date";
  inputType?: string;
  defaultValue?: string;
  variant?: "default" | "transparent";
}

export interface SelectField extends BaseField {
  type: "select";
  options: Array<{ label: React.ReactNode; value: string }>;
}

export interface TextareaField extends BaseField {
  type: "textarea";
}

export type FormField = InputField | SelectField | TextareaField;

export interface FormValues {
  contactChannel: string;
  contactInfo: string;
  followUpDate: string;
  followUpStatus: string;
  name: string;
  note: string;
  opportunity: string;
  servicesOfInterest: string;
  startDate: string;
  userStatus: string;
}
