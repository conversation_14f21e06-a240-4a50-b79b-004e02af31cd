import type { Task } from "@pages/all-leads/interface";

interface ProgressProps {
  tasks: Task[];
}

export const Progress = ({ tasks }: ProgressProps) => {
  const allTask = tasks.length;
  const completedTask = tasks.filter((task) => task.completed).length;
  const percentage = Math.round((completedTask / allTask) * 100);

  return (
    <div
      style={
        {
          "--size": "2rem",
          "--thickness": "0.2rem",
          "--value": percentage,
        } as React.CSSProperties
      }
      className="radial-progress progress-secondary text-xs"
    >
      <p className="text-base-content text-label-sm">{allTask - completedTask}</p>
    </div>
  );
};
