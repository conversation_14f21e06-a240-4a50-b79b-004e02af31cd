import { type InferInput, object, pipe, string } from "valibot";

const messages = {
  contactChannel: "contactChannel is required",
  name: "Name is required",
};

export const addLeadSchema: ReturnType<typeof object> = object({
  contactChannel: pipe(string(), require(messages.contactChannel)),
  contactInfo: string(),
  followUpDate: string(),
  followUpStatus: string(),
  name: pipe(string(), require(messages.name)),
  note: string(),
  opportunity: string(),
  servicesOfInterest: string(),
  startDate: string(),
});

export type AddLeadSchema = InferInput<typeof addLeadSchema>;
