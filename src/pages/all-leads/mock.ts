import type { LeadProps } from "./interface";

export const leads: LeadProps[] = [
  {
    contactChannel: "line",
    followUpDate: "01/01/2021",
    followUpStatus: "contacted",
    name: "<PERSON>",
    opportunity: "hot",
    servicesOfInterest: "botox",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
    userStatus: "draft",
  },
  {
    contactChannel: "instagram",
    followUpDate: "01/01/2021",
    followUpStatus: "interested",
    name: "John Donut",
    opportunity: "hot",
    servicesOfInterest: "hifu",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
    userStatus: "active",
  },
  {
    contactChannel: "tiktok",
    followUpDate: "01/01/2021",
    followUpStatus: "pending",
    name: "<PERSON>",
    opportunity: "warm",
    servicesOfInterest: "thermage",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
    userStatus: "completed",
  },
  {
    contactChannel: "facebook",
    followUpDate: "01/01/2021",
    followUpStatus: "not_interested",
    name: "John Doreamon",
    opportunity: "cold",
    servicesOfInterest: "juvelook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
    userStatus: "suspended",
  },
];
